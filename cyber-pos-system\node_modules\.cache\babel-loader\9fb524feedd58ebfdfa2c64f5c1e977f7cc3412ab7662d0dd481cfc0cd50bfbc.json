{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { collection, query, where, orderBy, limit, getDocs, Timestamp } from 'firebase/firestore';\nimport { db } from '../config/firebase';\nexport const useDashboard = () => {\n  _s();\n  const [stats, setStats] = useState({\n    todaysSales: 0,\n    todaysTransactions: 0,\n    lowStockCount: 0,\n    activeCustomers: 0,\n    salesChange: 0,\n    transactionsChange: 0\n  });\n  const [recentTransactions, setRecentTransactions] = useState([]);\n  const [lowStockItems, setLowStockItems] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Get date range for today\n  const getTodayRange = () => {\n    const today = new Date();\n    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());\n    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);\n    return {\n      startOfDay,\n      endOfDay\n    };\n  };\n\n  // Get date range for yesterday\n  const getYesterdayRange = () => {\n    const yesterday = new Date();\n    yesterday.setDate(yesterday.getDate() - 1);\n    const startOfDay = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate());\n    const endOfDay = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate() + 1);\n    return {\n      startOfDay,\n      endOfDay\n    };\n  };\n\n  // Fetch today's transactions\n  const fetchTodaysTransactions = async () => {\n    try {\n      const {\n        startOfDay,\n        endOfDay\n      } = getTodayRange();\n      const transactionsQuery = query(collection(db, 'transactions'), where('createdAt', '>=', Timestamp.fromDate(startOfDay)), where('createdAt', '<', Timestamp.fromDate(endOfDay)), orderBy('createdAt', 'desc'));\n      const snapshot = await getDocs(transactionsQuery);\n      const transactions = [];\n      snapshot.forEach(doc => {\n        var _data$createdAt;\n        const data = doc.data();\n        transactions.push({\n          id: doc.id,\n          items: data.items || [],\n          subtotal: data.subtotal || 0,\n          discount: data.discount,\n          total: data.total || 0,\n          paymentMethod: data.paymentMethod || 'cash',\n          customerName: data.customerName || 'Walk-in Customer',\n          attendantId: data.attendantId || '',\n          attendantName: data.attendantName || '',\n          notes: data.notes,\n          createdAt: ((_data$createdAt = data.createdAt) === null || _data$createdAt === void 0 ? void 0 : _data$createdAt.toDate()) || new Date()\n        });\n      });\n      return transactions;\n    } catch (error) {\n      console.error('Error fetching today\\'s transactions:', error);\n      return [];\n    }\n  };\n\n  // Fetch yesterday's transactions for comparison\n  const fetchYesterdaysTransactions = async () => {\n    try {\n      const {\n        startOfDay,\n        endOfDay\n      } = getYesterdayRange();\n      const transactionsQuery = query(collection(db, 'transactions'), where('createdAt', '>=', Timestamp.fromDate(startOfDay)), where('createdAt', '<', Timestamp.fromDate(endOfDay)));\n      const snapshot = await getDocs(transactionsQuery);\n      const transactions = [];\n      snapshot.forEach(doc => {\n        var _data$createdAt2;\n        const data = doc.data();\n        transactions.push({\n          id: doc.id,\n          items: data.items || [],\n          subtotal: data.subtotal || 0,\n          discount: data.discount,\n          total: data.total || 0,\n          paymentMethod: data.paymentMethod || 'cash',\n          customerName: data.customerName || 'Walk-in Customer',\n          attendantId: data.attendantId || '',\n          attendantName: data.attendantName || '',\n          notes: data.notes,\n          createdAt: ((_data$createdAt2 = data.createdAt) === null || _data$createdAt2 === void 0 ? void 0 : _data$createdAt2.toDate()) || new Date()\n        });\n      });\n      return transactions;\n    } catch (error) {\n      console.error('Error fetching yesterday\\'s transactions:', error);\n      return [];\n    }\n  };\n\n  // Fetch recent transactions for display\n  const fetchRecentTransactions = async () => {\n    try {\n      const recentQuery = query(collection(db, 'transactions'), orderBy('createdAt', 'desc'), limit(5));\n      const snapshot = await getDocs(recentQuery);\n      const recent = [];\n      snapshot.forEach(doc => {\n        var _data$createdAt3, _data$items, _data$items$, _data$items2;\n        const data = doc.data();\n        const createdAt = ((_data$createdAt3 = data.createdAt) === null || _data$createdAt3 === void 0 ? void 0 : _data$createdAt3.toDate()) || new Date();\n        const timeAgo = getTimeAgo(createdAt);\n\n        // Get primary service/product type\n        const primaryType = ((_data$items = data.items) === null || _data$items === void 0 ? void 0 : (_data$items$ = _data$items[0]) === null || _data$items$ === void 0 ? void 0 : _data$items$.type) === 'service' ? data.items[0].name : 'Stationery';\n        recent.push({\n          id: doc.id,\n          customerName: data.customerName || 'Walk-in Customer',\n          amount: data.total || 0,\n          time: timeAgo,\n          type: primaryType,\n          items: ((_data$items2 = data.items) === null || _data$items2 === void 0 ? void 0 : _data$items2.map(item => item.name)) || []\n        });\n      });\n      return recent;\n    } catch (error) {\n      console.error('Error fetching recent transactions:', error);\n      return [];\n    }\n  };\n\n  // Fetch low stock products\n  const fetchLowStockProducts = async () => {\n    try {\n      const productsQuery = query(collection(db, 'products'), where('isActive', '==', true));\n      const snapshot = await getDocs(productsQuery);\n      const lowStock = [];\n      snapshot.forEach(doc => {\n        const data = doc.data();\n        const stockQuantity = data.stockQuantity || 0;\n        const reorderLevel = data.reorderLevel || 0;\n        if (stockQuantity <= reorderLevel) {\n          lowStock.push({\n            id: doc.id,\n            name: data.name || '',\n            current: stockQuantity,\n            minimum: reorderLevel,\n            category: data.category || ''\n          });\n        }\n      });\n      return lowStock.sort((a, b) => a.current - a.minimum - (b.current - b.minimum));\n    } catch (error) {\n      console.error('Error fetching low stock products:', error);\n      return [];\n    }\n  };\n\n  // Helper function to get time ago string\n  const getTimeAgo = date => {\n    const now = new Date();\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'Just now';\n    if (diffInMinutes < 60) return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;\n  };\n\n  // Calculate percentage change\n  const calculateChange = (current, previous) => {\n    if (previous === 0) return current > 0 ? 100 : 0;\n    return Math.round((current - previous) / previous * 100);\n  };\n\n  // Load all dashboard data\n  const loadDashboardData = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const [todaysTransactions, yesterdaysTransactions, recentTxns, lowStockProducts] = await Promise.all([fetchTodaysTransactions(), fetchYesterdaysTransactions(), fetchRecentTransactions(), fetchLowStockProducts()]);\n\n      // Calculate today's stats\n      const todaysSales = todaysTransactions.reduce((sum, txn) => sum + txn.total, 0);\n      const todaysCount = todaysTransactions.length;\n\n      // Calculate yesterday's stats for comparison\n      const yesterdaysSales = yesterdaysTransactions.reduce((sum, txn) => sum + txn.total, 0);\n      const yesterdaysCount = yesterdaysTransactions.length;\n\n      // Calculate changes\n      const salesChange = calculateChange(todaysSales, yesterdaysSales);\n      const transactionsChange = calculateChange(todaysCount, yesterdaysCount);\n\n      // Get unique customers from recent transactions\n      const uniqueCustomers = new Set(todaysTransactions.filter(txn => txn.customerName && txn.customerName !== 'Walk-in Customer').map(txn => txn.customerName));\n      setStats({\n        todaysSales,\n        todaysTransactions: todaysCount,\n        lowStockCount: lowStockProducts.length,\n        activeCustomers: uniqueCustomers.size,\n        salesChange,\n        transactionsChange\n      });\n      setRecentTransactions(recentTxns);\n      setLowStockItems(lowStockProducts.slice(0, 5)); // Show top 5 low stock items\n    } catch (error) {\n      console.error('Error loading dashboard data:', error);\n      setError('Failed to load dashboard data');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Load data on component mount\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n\n  // Refresh data function\n  const refreshData = () => {\n    loadDashboardData();\n  };\n  return {\n    stats,\n    recentTransactions,\n    lowStockItems,\n    loading,\n    error,\n    refreshData\n  };\n};\n_s(useDashboard, \"qyUKZG0lynwfExU7mlpVtS2DO4Y=\");", "map": {"version": 3, "names": ["useState", "useEffect", "collection", "query", "where", "orderBy", "limit", "getDocs", "Timestamp", "db", "useDashboard", "_s", "stats", "setStats", "todaysSales", "todaysTransactions", "lowStockCount", "activeCustomers", "salesChange", "transactionsChange", "recentTransactions", "setRecentTransactions", "lowStockItems", "setLowStockItems", "loading", "setLoading", "error", "setError", "getTodayRange", "today", "Date", "startOfDay", "getFullYear", "getMonth", "getDate", "endOfDay", "getYesterdayRange", "yesterday", "setDate", "fetchTodaysTransactions", "transactionsQuery", "fromDate", "snapshot", "transactions", "for<PERSON>ach", "doc", "_data$createdAt", "data", "push", "id", "items", "subtotal", "discount", "total", "paymentMethod", "customerName", "attendantId", "<PERSON><PERSON><PERSON>", "notes", "createdAt", "toDate", "console", "fetchYesterdaysTransactions", "_data$createdAt2", "fetchRecentTransactions", "<PERSON><PERSON><PERSON>y", "recent", "_data$createdAt3", "_data$items", "_data$items$", "_data$items2", "timeAgo", "getTimeAgo", "primaryType", "type", "name", "amount", "time", "map", "item", "fetchLowStockProducts", "productsQuery", "lowStock", "stockQuantity", "reorderLevel", "current", "minimum", "category", "sort", "a", "b", "date", "now", "diffInMinutes", "Math", "floor", "getTime", "diffInHours", "diffInDays", "calculateChange", "previous", "round", "loadDashboardData", "yesterdaysTransactions", "recentTxns", "lowStockProducts", "Promise", "all", "reduce", "sum", "txn", "todaysCount", "length", "yesterdaysSales", "yesterdaysCount", "uniqueCustomers", "Set", "filter", "size", "slice", "refreshData"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/hooks/useDashboard.ts"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport {\n  collection,\n  query,\n  where,\n  orderBy,\n  limit,\n  getDocs,\n  Timestamp\n} from 'firebase/firestore';\nimport { db } from '../config/firebase';\nimport { Transaction, Product } from '../types';\n\nexport interface DashboardStats {\n  todaysSales: number;\n  todaysTransactions: number;\n  lowStockCount: number;\n  activeCustomers: number;\n  salesChange: number;\n  transactionsChange: number;\n}\n\nexport interface RecentTransaction {\n  id: string;\n  customerName: string;\n  amount: number;\n  time: string;\n  type: string;\n  items: string[];\n}\n\nexport interface LowStockItem {\n  id: string;\n  name: string;\n  current: number;\n  minimum: number;\n  category: string;\n}\n\nexport const useDashboard = () => {\n  const [stats, setStats] = useState<DashboardStats>({\n    todaysSales: 0,\n    todaysTransactions: 0,\n    lowStockCount: 0,\n    activeCustomers: 0,\n    salesChange: 0,\n    transactionsChange: 0,\n  });\n  const [recentTransactions, setRecentTransactions] = useState<RecentTransaction[]>([]);\n  const [lowStockItems, setLowStockItems] = useState<LowStockItem[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // Get date range for today\n  const getTodayRange = () => {\n    const today = new Date();\n    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());\n    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);\n    return { startOfDay, endOfDay };\n  };\n\n  // Get date range for yesterday\n  const getYesterdayRange = () => {\n    const yesterday = new Date();\n    yesterday.setDate(yesterday.getDate() - 1);\n    const startOfDay = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate());\n    const endOfDay = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate() + 1);\n    return { startOfDay, endOfDay };\n  };\n\n  // Fetch today's transactions\n  const fetchTodaysTransactions = async () => {\n    try {\n      const { startOfDay, endOfDay } = getTodayRange();\n      const transactionsQuery = query(\n        collection(db, 'transactions'),\n        where('createdAt', '>=', Timestamp.fromDate(startOfDay)),\n        where('createdAt', '<', Timestamp.fromDate(endOfDay)),\n        orderBy('createdAt', 'desc')\n      );\n\n      const snapshot = await getDocs(transactionsQuery);\n      const transactions: Transaction[] = [];\n      \n      snapshot.forEach((doc) => {\n        const data = doc.data();\n        transactions.push({\n          id: doc.id,\n          items: data.items || [],\n          subtotal: data.subtotal || 0,\n          discount: data.discount,\n          total: data.total || 0,\n          paymentMethod: data.paymentMethod || 'cash',\n          customerName: data.customerName || 'Walk-in Customer',\n          attendantId: data.attendantId || '',\n          attendantName: data.attendantName || '',\n          notes: data.notes,\n          createdAt: data.createdAt?.toDate() || new Date(),\n        });\n      });\n\n      return transactions;\n    } catch (error) {\n      console.error('Error fetching today\\'s transactions:', error);\n      return [];\n    }\n  };\n\n  // Fetch yesterday's transactions for comparison\n  const fetchYesterdaysTransactions = async () => {\n    try {\n      const { startOfDay, endOfDay } = getYesterdayRange();\n      const transactionsQuery = query(\n        collection(db, 'transactions'),\n        where('createdAt', '>=', Timestamp.fromDate(startOfDay)),\n        where('createdAt', '<', Timestamp.fromDate(endOfDay))\n      );\n\n      const snapshot = await getDocs(transactionsQuery);\n      const transactions: Transaction[] = [];\n      \n      snapshot.forEach((doc) => {\n        const data = doc.data();\n        transactions.push({\n          id: doc.id,\n          items: data.items || [],\n          subtotal: data.subtotal || 0,\n          discount: data.discount,\n          total: data.total || 0,\n          paymentMethod: data.paymentMethod || 'cash',\n          customerName: data.customerName || 'Walk-in Customer',\n          attendantId: data.attendantId || '',\n          attendantName: data.attendantName || '',\n          notes: data.notes,\n          createdAt: data.createdAt?.toDate() || new Date(),\n        });\n      });\n\n      return transactions;\n    } catch (error) {\n      console.error('Error fetching yesterday\\'s transactions:', error);\n      return [];\n    }\n  };\n\n  // Fetch recent transactions for display\n  const fetchRecentTransactions = async () => {\n    try {\n      const recentQuery = query(\n        collection(db, 'transactions'),\n        orderBy('createdAt', 'desc'),\n        limit(5)\n      );\n\n      const snapshot = await getDocs(recentQuery);\n      const recent: RecentTransaction[] = [];\n\n      snapshot.forEach((doc) => {\n        const data = doc.data();\n        const createdAt = data.createdAt?.toDate() || new Date();\n        const timeAgo = getTimeAgo(createdAt);\n        \n        // Get primary service/product type\n        const primaryType = data.items?.[0]?.type === 'service' ? \n          data.items[0].name : \n          'Stationery';\n\n        recent.push({\n          id: doc.id,\n          customerName: data.customerName || 'Walk-in Customer',\n          amount: data.total || 0,\n          time: timeAgo,\n          type: primaryType,\n          items: data.items?.map((item: any) => item.name) || [],\n        });\n      });\n\n      return recent;\n    } catch (error) {\n      console.error('Error fetching recent transactions:', error);\n      return [];\n    }\n  };\n\n  // Fetch low stock products\n  const fetchLowStockProducts = async () => {\n    try {\n      const productsQuery = query(\n        collection(db, 'products'),\n        where('isActive', '==', true)\n      );\n\n      const snapshot = await getDocs(productsQuery);\n      const lowStock: LowStockItem[] = [];\n\n      snapshot.forEach((doc) => {\n        const data = doc.data();\n        const stockQuantity = data.stockQuantity || 0;\n        const reorderLevel = data.reorderLevel || 0;\n\n        if (stockQuantity <= reorderLevel) {\n          lowStock.push({\n            id: doc.id,\n            name: data.name || '',\n            current: stockQuantity,\n            minimum: reorderLevel,\n            category: data.category || '',\n          });\n        }\n      });\n\n      return lowStock.sort((a, b) => (a.current - a.minimum) - (b.current - b.minimum));\n    } catch (error) {\n      console.error('Error fetching low stock products:', error);\n      return [];\n    }\n  };\n\n  // Helper function to get time ago string\n  const getTimeAgo = (date: Date): string => {\n    const now = new Date();\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n\n    if (diffInMinutes < 1) return 'Just now';\n    if (diffInMinutes < 60) return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;\n    \n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;\n    \n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;\n  };\n\n  // Calculate percentage change\n  const calculateChange = (current: number, previous: number): number => {\n    if (previous === 0) return current > 0 ? 100 : 0;\n    return Math.round(((current - previous) / previous) * 100);\n  };\n\n  // Load all dashboard data\n  const loadDashboardData = async () => {\n    setLoading(true);\n    setError(null);\n\n    try {\n      const [\n        todaysTransactions,\n        yesterdaysTransactions,\n        recentTxns,\n        lowStockProducts\n      ] = await Promise.all([\n        fetchTodaysTransactions(),\n        fetchYesterdaysTransactions(),\n        fetchRecentTransactions(),\n        fetchLowStockProducts()\n      ]);\n\n      // Calculate today's stats\n      const todaysSales = todaysTransactions.reduce((sum, txn) => sum + txn.total, 0);\n      const todaysCount = todaysTransactions.length;\n\n      // Calculate yesterday's stats for comparison\n      const yesterdaysSales = yesterdaysTransactions.reduce((sum, txn) => sum + txn.total, 0);\n      const yesterdaysCount = yesterdaysTransactions.length;\n\n      // Calculate changes\n      const salesChange = calculateChange(todaysSales, yesterdaysSales);\n      const transactionsChange = calculateChange(todaysCount, yesterdaysCount);\n\n      // Get unique customers from recent transactions\n      const uniqueCustomers = new Set(\n        todaysTransactions\n          .filter(txn => txn.customerName && txn.customerName !== 'Walk-in Customer')\n          .map(txn => txn.customerName)\n      );\n\n      setStats({\n        todaysSales,\n        todaysTransactions: todaysCount,\n        lowStockCount: lowStockProducts.length,\n        activeCustomers: uniqueCustomers.size,\n        salesChange,\n        transactionsChange,\n      });\n\n      setRecentTransactions(recentTxns);\n      setLowStockItems(lowStockProducts.slice(0, 5)); // Show top 5 low stock items\n\n    } catch (error) {\n      console.error('Error loading dashboard data:', error);\n      setError('Failed to load dashboard data');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Load data on component mount\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n\n  // Refresh data function\n  const refreshData = () => {\n    loadDashboardData();\n  };\n\n  return {\n    stats,\n    recentTransactions,\n    lowStockItems,\n    loading,\n    error,\n    refreshData,\n  };\n};\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SACEC,UAAU,EACVC,KAAK,EACLC,KAAK,EACLC,OAAO,EACPC,KAAK,EACLC,OAAO,EACPC,SAAS,QACJ,oBAAoB;AAC3B,SAASC,EAAE,QAAQ,oBAAoB;AA6BvC,OAAO,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAiB;IACjDc,WAAW,EAAE,CAAC;IACdC,kBAAkB,EAAE,CAAC;IACrBC,aAAa,EAAE,CAAC;IAChBC,eAAe,EAAE,CAAC;IAClBC,WAAW,EAAE,CAAC;IACdC,kBAAkB,EAAE;EACtB,CAAC,CAAC;EACF,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrB,QAAQ,CAAsB,EAAE,CAAC;EACrF,MAAM,CAACsB,aAAa,EAAEC,gBAAgB,CAAC,GAAGvB,QAAQ,CAAiB,EAAE,CAAC;EACtE,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAgB,IAAI,CAAC;;EAEvD;EACA,MAAM4B,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;IACxB,MAAMC,UAAU,GAAG,IAAID,IAAI,CAACD,KAAK,CAACG,WAAW,CAAC,CAAC,EAAEH,KAAK,CAACI,QAAQ,CAAC,CAAC,EAAEJ,KAAK,CAACK,OAAO,CAAC,CAAC,CAAC;IACnF,MAAMC,QAAQ,GAAG,IAAIL,IAAI,CAACD,KAAK,CAACG,WAAW,CAAC,CAAC,EAAEH,KAAK,CAACI,QAAQ,CAAC,CAAC,EAAEJ,KAAK,CAACK,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;IACrF,OAAO;MAAEH,UAAU;MAAEI;IAAS,CAAC;EACjC,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,SAAS,GAAG,IAAIP,IAAI,CAAC,CAAC;IAC5BO,SAAS,CAACC,OAAO,CAACD,SAAS,CAACH,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;IAC1C,MAAMH,UAAU,GAAG,IAAID,IAAI,CAACO,SAAS,CAACL,WAAW,CAAC,CAAC,EAAEK,SAAS,CAACJ,QAAQ,CAAC,CAAC,EAAEI,SAAS,CAACH,OAAO,CAAC,CAAC,CAAC;IAC/F,MAAMC,QAAQ,GAAG,IAAIL,IAAI,CAACO,SAAS,CAACL,WAAW,CAAC,CAAC,EAAEK,SAAS,CAACJ,QAAQ,CAAC,CAAC,EAAEI,SAAS,CAACH,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;IACjG,OAAO;MAAEH,UAAU;MAAEI;IAAS,CAAC;EACjC,CAAC;;EAED;EACA,MAAMI,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1C,IAAI;MACF,MAAM;QAAER,UAAU;QAAEI;MAAS,CAAC,GAAGP,aAAa,CAAC,CAAC;MAChD,MAAMY,iBAAiB,GAAGrC,KAAK,CAC7BD,UAAU,CAACO,EAAE,EAAE,cAAc,CAAC,EAC9BL,KAAK,CAAC,WAAW,EAAE,IAAI,EAAEI,SAAS,CAACiC,QAAQ,CAACV,UAAU,CAAC,CAAC,EACxD3B,KAAK,CAAC,WAAW,EAAE,GAAG,EAAEI,SAAS,CAACiC,QAAQ,CAACN,QAAQ,CAAC,CAAC,EACrD9B,OAAO,CAAC,WAAW,EAAE,MAAM,CAC7B,CAAC;MAED,MAAMqC,QAAQ,GAAG,MAAMnC,OAAO,CAACiC,iBAAiB,CAAC;MACjD,MAAMG,YAA2B,GAAG,EAAE;MAEtCD,QAAQ,CAACE,OAAO,CAAEC,GAAG,IAAK;QAAA,IAAAC,eAAA;QACxB,MAAMC,IAAI,GAAGF,GAAG,CAACE,IAAI,CAAC,CAAC;QACvBJ,YAAY,CAACK,IAAI,CAAC;UAChBC,EAAE,EAAEJ,GAAG,CAACI,EAAE;UACVC,KAAK,EAAEH,IAAI,CAACG,KAAK,IAAI,EAAE;UACvBC,QAAQ,EAAEJ,IAAI,CAACI,QAAQ,IAAI,CAAC;UAC5BC,QAAQ,EAAEL,IAAI,CAACK,QAAQ;UACvBC,KAAK,EAAEN,IAAI,CAACM,KAAK,IAAI,CAAC;UACtBC,aAAa,EAAEP,IAAI,CAACO,aAAa,IAAI,MAAM;UAC3CC,YAAY,EAAER,IAAI,CAACQ,YAAY,IAAI,kBAAkB;UACrDC,WAAW,EAAET,IAAI,CAACS,WAAW,IAAI,EAAE;UACnCC,aAAa,EAAEV,IAAI,CAACU,aAAa,IAAI,EAAE;UACvCC,KAAK,EAAEX,IAAI,CAACW,KAAK;UACjBC,SAAS,EAAE,EAAAb,eAAA,GAAAC,IAAI,CAACY,SAAS,cAAAb,eAAA,uBAAdA,eAAA,CAAgBc,MAAM,CAAC,CAAC,KAAI,IAAI9B,IAAI,CAAC;QAClD,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF,OAAOa,YAAY;IACrB,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdmC,OAAO,CAACnC,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,OAAO,EAAE;IACX;EACF,CAAC;;EAED;EACA,MAAMoC,2BAA2B,GAAG,MAAAA,CAAA,KAAY;IAC9C,IAAI;MACF,MAAM;QAAE/B,UAAU;QAAEI;MAAS,CAAC,GAAGC,iBAAiB,CAAC,CAAC;MACpD,MAAMI,iBAAiB,GAAGrC,KAAK,CAC7BD,UAAU,CAACO,EAAE,EAAE,cAAc,CAAC,EAC9BL,KAAK,CAAC,WAAW,EAAE,IAAI,EAAEI,SAAS,CAACiC,QAAQ,CAACV,UAAU,CAAC,CAAC,EACxD3B,KAAK,CAAC,WAAW,EAAE,GAAG,EAAEI,SAAS,CAACiC,QAAQ,CAACN,QAAQ,CAAC,CACtD,CAAC;MAED,MAAMO,QAAQ,GAAG,MAAMnC,OAAO,CAACiC,iBAAiB,CAAC;MACjD,MAAMG,YAA2B,GAAG,EAAE;MAEtCD,QAAQ,CAACE,OAAO,CAAEC,GAAG,IAAK;QAAA,IAAAkB,gBAAA;QACxB,MAAMhB,IAAI,GAAGF,GAAG,CAACE,IAAI,CAAC,CAAC;QACvBJ,YAAY,CAACK,IAAI,CAAC;UAChBC,EAAE,EAAEJ,GAAG,CAACI,EAAE;UACVC,KAAK,EAAEH,IAAI,CAACG,KAAK,IAAI,EAAE;UACvBC,QAAQ,EAAEJ,IAAI,CAACI,QAAQ,IAAI,CAAC;UAC5BC,QAAQ,EAAEL,IAAI,CAACK,QAAQ;UACvBC,KAAK,EAAEN,IAAI,CAACM,KAAK,IAAI,CAAC;UACtBC,aAAa,EAAEP,IAAI,CAACO,aAAa,IAAI,MAAM;UAC3CC,YAAY,EAAER,IAAI,CAACQ,YAAY,IAAI,kBAAkB;UACrDC,WAAW,EAAET,IAAI,CAACS,WAAW,IAAI,EAAE;UACnCC,aAAa,EAAEV,IAAI,CAACU,aAAa,IAAI,EAAE;UACvCC,KAAK,EAAEX,IAAI,CAACW,KAAK;UACjBC,SAAS,EAAE,EAAAI,gBAAA,GAAAhB,IAAI,CAACY,SAAS,cAAAI,gBAAA,uBAAdA,gBAAA,CAAgBH,MAAM,CAAC,CAAC,KAAI,IAAI9B,IAAI,CAAC;QAClD,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF,OAAOa,YAAY;IACrB,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdmC,OAAO,CAACnC,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,OAAO,EAAE;IACX;EACF,CAAC;;EAED;EACA,MAAMsC,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1C,IAAI;MACF,MAAMC,WAAW,GAAG9D,KAAK,CACvBD,UAAU,CAACO,EAAE,EAAE,cAAc,CAAC,EAC9BJ,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,EAC5BC,KAAK,CAAC,CAAC,CACT,CAAC;MAED,MAAMoC,QAAQ,GAAG,MAAMnC,OAAO,CAAC0D,WAAW,CAAC;MAC3C,MAAMC,MAA2B,GAAG,EAAE;MAEtCxB,QAAQ,CAACE,OAAO,CAAEC,GAAG,IAAK;QAAA,IAAAsB,gBAAA,EAAAC,WAAA,EAAAC,YAAA,EAAAC,YAAA;QACxB,MAAMvB,IAAI,GAAGF,GAAG,CAACE,IAAI,CAAC,CAAC;QACvB,MAAMY,SAAS,GAAG,EAAAQ,gBAAA,GAAApB,IAAI,CAACY,SAAS,cAAAQ,gBAAA,uBAAdA,gBAAA,CAAgBP,MAAM,CAAC,CAAC,KAAI,IAAI9B,IAAI,CAAC,CAAC;QACxD,MAAMyC,OAAO,GAAGC,UAAU,CAACb,SAAS,CAAC;;QAErC;QACA,MAAMc,WAAW,GAAG,EAAAL,WAAA,GAAArB,IAAI,CAACG,KAAK,cAAAkB,WAAA,wBAAAC,YAAA,GAAVD,WAAA,CAAa,CAAC,CAAC,cAAAC,YAAA,uBAAfA,YAAA,CAAiBK,IAAI,MAAK,SAAS,GACrD3B,IAAI,CAACG,KAAK,CAAC,CAAC,CAAC,CAACyB,IAAI,GAClB,YAAY;QAEdT,MAAM,CAAClB,IAAI,CAAC;UACVC,EAAE,EAAEJ,GAAG,CAACI,EAAE;UACVM,YAAY,EAAER,IAAI,CAACQ,YAAY,IAAI,kBAAkB;UACrDqB,MAAM,EAAE7B,IAAI,CAACM,KAAK,IAAI,CAAC;UACvBwB,IAAI,EAAEN,OAAO;UACbG,IAAI,EAAED,WAAW;UACjBvB,KAAK,EAAE,EAAAoB,YAAA,GAAAvB,IAAI,CAACG,KAAK,cAAAoB,YAAA,uBAAVA,YAAA,CAAYQ,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACJ,IAAI,CAAC,KAAI;QACtD,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF,OAAOT,MAAM;IACf,CAAC,CAAC,OAAOxC,KAAK,EAAE;MACdmC,OAAO,CAACnC,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAO,EAAE;IACX;EACF,CAAC;;EAED;EACA,MAAMsD,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACF,MAAMC,aAAa,GAAG9E,KAAK,CACzBD,UAAU,CAACO,EAAE,EAAE,UAAU,CAAC,EAC1BL,KAAK,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,CAC9B,CAAC;MAED,MAAMsC,QAAQ,GAAG,MAAMnC,OAAO,CAAC0E,aAAa,CAAC;MAC7C,MAAMC,QAAwB,GAAG,EAAE;MAEnCxC,QAAQ,CAACE,OAAO,CAAEC,GAAG,IAAK;QACxB,MAAME,IAAI,GAAGF,GAAG,CAACE,IAAI,CAAC,CAAC;QACvB,MAAMoC,aAAa,GAAGpC,IAAI,CAACoC,aAAa,IAAI,CAAC;QAC7C,MAAMC,YAAY,GAAGrC,IAAI,CAACqC,YAAY,IAAI,CAAC;QAE3C,IAAID,aAAa,IAAIC,YAAY,EAAE;UACjCF,QAAQ,CAAClC,IAAI,CAAC;YACZC,EAAE,EAAEJ,GAAG,CAACI,EAAE;YACV0B,IAAI,EAAE5B,IAAI,CAAC4B,IAAI,IAAI,EAAE;YACrBU,OAAO,EAAEF,aAAa;YACtBG,OAAO,EAAEF,YAAY;YACrBG,QAAQ,EAAExC,IAAI,CAACwC,QAAQ,IAAI;UAC7B,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MAEF,OAAOL,QAAQ,CAACM,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAMD,CAAC,CAACJ,OAAO,GAAGI,CAAC,CAACH,OAAO,IAAKI,CAAC,CAACL,OAAO,GAAGK,CAAC,CAACJ,OAAO,CAAC,CAAC;IACnF,CAAC,CAAC,OAAO5D,KAAK,EAAE;MACdmC,OAAO,CAACnC,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,OAAO,EAAE;IACX;EACF,CAAC;;EAED;EACA,MAAM8C,UAAU,GAAImB,IAAU,IAAa;IACzC,MAAMC,GAAG,GAAG,IAAI9D,IAAI,CAAC,CAAC;IACtB,MAAM+D,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC,GAAGL,IAAI,CAACK,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAEhF,IAAIH,aAAa,GAAG,CAAC,EAAE,OAAO,UAAU;IACxC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAO,GAAGA,aAAa,UAAUA,aAAa,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,MAAM;IAE3F,MAAMI,WAAW,GAAGH,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAC;IAClD,IAAII,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,QAAQA,WAAW,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,MAAM;IAEnF,MAAMC,UAAU,GAAGJ,IAAI,CAACC,KAAK,CAACE,WAAW,GAAG,EAAE,CAAC;IAC/C,OAAO,GAAGC,UAAU,OAAOA,UAAU,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,MAAM;EAC5D,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAACd,OAAe,EAAEe,QAAgB,KAAa;IACrE,IAAIA,QAAQ,KAAK,CAAC,EAAE,OAAOf,OAAO,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC;IAChD,OAAOS,IAAI,CAACO,KAAK,CAAE,CAAChB,OAAO,GAAGe,QAAQ,IAAIA,QAAQ,GAAI,GAAG,CAAC;EAC5D,CAAC;;EAED;EACA,MAAME,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC7E,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAM,CACJZ,kBAAkB,EAClBwF,sBAAsB,EACtBC,UAAU,EACVC,gBAAgB,CACjB,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACpBpE,uBAAuB,CAAC,CAAC,EACzBuB,2BAA2B,CAAC,CAAC,EAC7BE,uBAAuB,CAAC,CAAC,EACzBgB,qBAAqB,CAAC,CAAC,CACxB,CAAC;;MAEF;MACA,MAAMlE,WAAW,GAAGC,kBAAkB,CAAC6F,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAACzD,KAAK,EAAE,CAAC,CAAC;MAC/E,MAAM0D,WAAW,GAAGhG,kBAAkB,CAACiG,MAAM;;MAE7C;MACA,MAAMC,eAAe,GAAGV,sBAAsB,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAACzD,KAAK,EAAE,CAAC,CAAC;MACvF,MAAM6D,eAAe,GAAGX,sBAAsB,CAACS,MAAM;;MAErD;MACA,MAAM9F,WAAW,GAAGiF,eAAe,CAACrF,WAAW,EAAEmG,eAAe,CAAC;MACjE,MAAM9F,kBAAkB,GAAGgF,eAAe,CAACY,WAAW,EAAEG,eAAe,CAAC;;MAExE;MACA,MAAMC,eAAe,GAAG,IAAIC,GAAG,CAC7BrG,kBAAkB,CACfsG,MAAM,CAACP,GAAG,IAAIA,GAAG,CAACvD,YAAY,IAAIuD,GAAG,CAACvD,YAAY,KAAK,kBAAkB,CAAC,CAC1EuB,GAAG,CAACgC,GAAG,IAAIA,GAAG,CAACvD,YAAY,CAChC,CAAC;MAED1C,QAAQ,CAAC;QACPC,WAAW;QACXC,kBAAkB,EAAEgG,WAAW;QAC/B/F,aAAa,EAAEyF,gBAAgB,CAACO,MAAM;QACtC/F,eAAe,EAAEkG,eAAe,CAACG,IAAI;QACrCpG,WAAW;QACXC;MACF,CAAC,CAAC;MAEFE,qBAAqB,CAACmF,UAAU,CAAC;MACjCjF,gBAAgB,CAACkF,gBAAgB,CAACc,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAElD,CAAC,CAAC,OAAO7F,KAAK,EAAE;MACdmC,OAAO,CAACnC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDC,QAAQ,CAAC,+BAA+B,CAAC;IAC3C,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACAxB,SAAS,CAAC,MAAM;IACdqG,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMkB,WAAW,GAAGA,CAAA,KAAM;IACxBlB,iBAAiB,CAAC,CAAC;EACrB,CAAC;EAED,OAAO;IACL1F,KAAK;IACLQ,kBAAkB;IAClBE,aAAa;IACbE,OAAO;IACPE,KAAK;IACL8F;EACF,CAAC;AACH,CAAC;AAAC7G,EAAA,CAnRWD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}