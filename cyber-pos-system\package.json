{"name": "cyber-pos-system", "version": "0.1.0", "private": true, "dependencies": {"@tailwindcss/forms": "^0.5.10", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/react-router-dom": "^5.3.3", "chart.js": "^4.5.0", "firebase": "^11.9.1", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.523.0", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "test:coverage": "react-scripts test --coverage --watchAll=false", "test:ci": "CI=true react-scripts test --coverage --watchAll=false", "build:analyze": "npm run build && npx webpack-bundle-analyzer build/static/js/*.js", "firebase:emulators": "cd .. && firebase emulators:start", "firebase:deploy": "npm run build && cd .. && firebase deploy", "firebase:deploy:hosting": "npm run build && cd .. && firebase deploy --only hosting", "firebase:deploy:rules": "cd .. && firebase deploy --only firestore:rules,storage:rules", "dev:with-emulators": "concurrently \"npm run firebase:emulators\" \"npm start\"", "setup:dev": "chmod +x scripts/setup.sh && ./scripts/setup.sh dev", "setup:prod": "chmod +x scripts/setup.sh && ./scripts/setup.sh prod", "lint": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,css,md}", "type-check": "tsc --noEmit", "pre-commit": "npm run lint && npm run type-check && npm run test:ci", "serve": "npm run build && npx serve -s build", "clean": "rm -rf build node_modules/.cache", "postinstall": "npm run type-check"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "concurrently": "^9.2.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17"}}